@echo off
echo Starting Restaurant API Setup...

echo.
echo 1. Checking .NET version...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found. Please install .NET 8 SDK
    pause
    exit /b 1
)

echo.
echo 2. Restoring packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

echo.
echo 3. Building solution...
dotnet build
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo 4. Updating database...
cd Restaurants.API
dotnet ef database update --project ../Restaurants.Infrastructure
if %errorlevel% neq 0 (
    echo ERROR: Database update failed
    pause
    exit /b 1
)

echo.
echo 5. Starting the application...
echo The API will be available at:
echo - HTTPS: https://localhost:7243
echo - Swagger: https://localhost:7243/swagger
echo.
echo Press Ctrl+C to stop the server
echo.

dotnet run