﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
		<PackageReference Include="MediatR" Version="12.2.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Options" Version="9.0.7" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
	</ItemGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\Restaurants.Domain\Restaurants.Domain.csproj" />
  </ItemGroup>

</Project>
