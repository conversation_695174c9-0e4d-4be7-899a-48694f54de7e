{"version": "2.0.0", "tasks": [{"label": "Setup and Run Restaurant API", "type": "shell", "command": "dotnet", "args": ["tool", "install", "--global", "dotnet-ef", "&&", "dotnet", "restore", "&&", "dotnet", "build", "&&", "cd", "Restaurants.API", "&&", "dotnet", "ef", "database", "update", "--project", "../Restaurants.Infrastructure", "&&", "dotnet", "run"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}}]}