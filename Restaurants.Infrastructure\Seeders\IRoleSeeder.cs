﻿using Microsoft.AspNetCore.Identity;
using Restaurants.Domain.Constants;
using Restaurants.Infrastructure.Persistence;

namespace Restaurants.Infrastructure.Seeders
{
    public interface IRoleSeeder
    {
        Task Seed();
    }

    public class RoleSeeder(RestaurantsDbContext dbContext) : IRoleSeeder
    {
        public async Task Seed()
        {
            if (!dbContext.Roles.Any())
            {
                var roles = GetRoles();
                dbContext.Roles.AddRange(roles);
                await dbContext.SaveChangesAsync();
            }
        }
        private IEnumerable<IdentityRole> GetRoles()
        {
            List<IdentityRole> roles =
                [
                    new (UserRoles.User)
                {
                    NormalizedName = UserRoles.User.ToUpper()
                },
                new (UserRoles.Owner)
                {
                    NormalizedName = UserRoles.Owner.ToUpper()
                },
                new (UserRoles.Admin)
                {
                    NormalizedName = UserRoles.Admin.ToUpper()
                },
                new (UserRoles.SuperAdmin)
                {
                    NormalizedName = UserRoles.SuperAdmin.ToUpper()
                }
            ];

            return roles;
        }
    }
}
