#!/bin/bash
echo "Starting Restaurant API Setup..."

echo ""
echo "1. Checking .NET version..."
dotnet --version
if [ $? -ne 0 ]; then
    echo "ERROR: .NET SDK not found. Please install .NET 8 SDK"
    exit 1
fi

echo ""
echo "2. Restoring packages..."
dotnet restore
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to restore packages"
    exit 1
fi

echo ""
echo "3. Building solution..."
dotnet build
if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    exit 1
fi

echo ""
echo "4. Updating database..."
cd Restaurants.API
dotnet ef database update --project ../Restaurants.Infrastructure
if [ $? -ne 0 ]; then
    echo "ERROR: Database update failed"
    exit 1
fi

echo ""
echo "5. Starting the application..."
echo "The API will be available at:"
echo "- HTTPS: https://localhost:7243"
echo "- Swagger: https://localhost:7243/swagger"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

dotnet run