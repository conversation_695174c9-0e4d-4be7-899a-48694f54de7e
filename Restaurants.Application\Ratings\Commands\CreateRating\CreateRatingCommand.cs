﻿using MediatR;
using System.ComponentModel;

namespace Restaurants.Application.Ratings.Commands.CreateRating
{
    public class CreateRatingCommand : IRequest<int>
    {
        [DefaultValue(3)]
        public int Stars { get; set; }

        [DefaultValue("Write a Comment")]
        public string? Comment { get; set; }

        [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Ahmed Ali")]
        public string CustomerName { get; set; } = default!;

        [DefaultValue("Garlic Bread")]
        public string DishName { get; set; } = default!;

        [<PERSON><PERSON>ultValue("Pizza House")]
        public string RestaurantName { get; set; } = default!;
    }

}
